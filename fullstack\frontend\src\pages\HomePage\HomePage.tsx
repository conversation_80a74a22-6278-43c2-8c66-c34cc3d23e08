import React, { useState } from "react";
import { HiBars3, HiTrash } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { useAuth } from "../../contexts/AuthContext";
import { usePicks } from "../../contexts/PicksContext";
import { getConfidenceColor } from "../../utils/colorUtils";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface OptimizerData {
  best_config: string;
  best_score: number;
  sorted_picks: any[];
  split_index?: number;
  subparlays: SubparlayColumn[];
}

function HomePage() {
  const { navigateToView } = useAuth();
  const { getPicks, removePick, totalPicksCount } = usePicks();
  const userPicks = getPicks();

  // Optimizer state
  const [isOptimizeExpanded, setIsOptimizeExpanded] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizerResult, setOptimizerResult] = useState<string>("");
  const [subparlays, setSubparlays] = useState<SubparlayColumn[]>([]);
  const [optimizerError, setOptimizerError] = useState<string>("");

  const API_BASE_URL = "/api";

  const handleAddPicksClick = () => {
    navigateToView("addPicks");
  };

  const handleOptimizeParlays = async () => {
    if (totalPicksCount < 2) {
      setOptimizerError("Need at least 2 picks to optimize parlays");
      return;
    }

    setIsOptimizing(true);
    setOptimizerError("");
    setOptimizerResult("");
    setSubparlays([]);

    try {
      // First, we need to submit the user picks to the backend
      // Transform PicksContext picks to the format expected by the backend
      const picksToSubmit = userPicks.map((pick, index) => ({
        name: `${pick.playerName} - ${pick.betType}`,
        odds: "1.85", // Default odds since user picks don't have odds
        confidence: pick.confidence || 75,
        mutual_exclusion: index, // Each pick gets its own group
        league: "Mixed", // Default league
        event_id: `user_pick_${pick.sourceId}`,
        bayesian_prob: "0.5",
        logistic_prob: "0.5",
        bayesian_conf: 0.5,
        stat_type: pick.betType,
        reusable: true,
        capital_limit: 0
      }));

      // Submit picks to backend
      const submitResponse = await fetch(`${API_BASE_URL}/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          picks: picksToSubmit,
        }),
      });

      if (!submitResponse.ok) {
        throw new Error(`Failed to submit picks: ${submitResponse.status}`);
      }

      // Now run the optimizer
      const optimizeResponse = await fetch(`${API_BASE_URL}/optimize_split`);
      if (!optimizeResponse.ok) {
        throw new Error(`Failed to optimize: ${optimizeResponse.status}`);
      }

      const data: OptimizerData = await optimizeResponse.json();

      setOptimizerResult(`${data.best_config} | Score: ${data.best_score}`);
      setSubparlays(data.subparlays || []);
      setIsOptimizeExpanded(true);

    } catch (error) {
      console.error("Error optimizing parlays:", error);
      setOptimizerError(
        `Error optimizing parlays: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#061844] text-[#58C612] flex flex-col items-center justify-center p-4 select-none">
      <div className="absolute top-15 left-10 sm:top-15 sm:left-15">
        <HiBars3 className="w-auto h-14 sm:h-16 text-white bg-[#233e6c] rounded-lg hover:text-white transition-colors duration-300 cursor-pointer" />
      </div>

      <div className="w-full h-[100px] flex justify-center mb-auto mt-5 mt-10">
        <header className="flex items-center justify-center">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="w-auto h-32 sm:h-40 select-none transition-all duration-300 ease-in-out"
          />
        </header>
      </div>

      {totalPicksCount === 0 ? (
        // Empty state
        <div className="text-center mx-auto mb-auto mt-[-25%] sm:mt-[-10%] ">
          <h2 className="text-white text-[400%] font-bold text-center">
            Time for a fresh start
          </h2>

          <p className="text-white text-[125%] sm:text-[200%] mb-8">
            You don't have any picks in your list
          </p>

          <button
            onClick={handleAddPicksClick}
            className="px-8 py-4 bg-[#233e6c] hover:bg-[#232d6c] text-white w-[60%] font-bold rounded-lg text-[100%] text-[150%] sm:text-[200%] transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
          >
            Add Picks
          </button>
        </div>
      ) : (
        // My Picks display
        <div className="w-full max-w-7xl mx-auto px-4 mb-auto mt-4">
          <div className="mb-6">
            <h2 className="text-white text-3xl sm:text-4xl font-bold">
              My Picks ({totalPicksCount})
            </h2>
          </div>

          {/* 2-column grid of picks */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {userPicks.map((pick) => (
              <div
                key={pick.id}
                className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%]"
              >
                <div className="flex flex-row gap-6 my-auto justify-center items-center">
                  {/* Player info */}
                  <div className="flex flex-col items-center md:flex-1 min-w-0">
                    <div
                      className="w-32 h-32 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                      style={{
                        border: `4px solid ${getConfidenceColor(pick.confidence || 75)}`,
                      }}
                    >
                      <IoShirtOutline
                        className="w-20 h-20 absolute"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      />
                      <div className="text-white font-bold text-lg sm:text-xl z-10 relative">
                        {pick.playerNumber}
                      </div>
                    </div>
                    <h3 className="font-bold text-lg text-center">
                      {pick.playerName}
                    </h3>
                    <p className="text-sm text-center">{pick.betType}</p>
                    <p className="text-xs text-gray-400 text-center">
                      {pick.gameInfo}
                    </p>
                    {pick.handicapperName && (
                      <p className="text-blue-400 text-xs mt-1 text-center">
                        Recommended by {pick.handicapperName}
                      </p>
                    )}
                  </div>

                  {/* Confidence and remove button */}
                  <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
                    <div className="text-center flex flex-col items-center justify-start h-full">
                      <div
                        className="text-[75px] md:text-[100px] font-bold mt-[-24px]"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      >
                        {pick.confidence || 75}
                      </div>
                      <div className="text-[24px] md:text-[24px] font-bold text-white mt-[-24px]">
                        Confidence
                        <br />
                        Rating
                      </div>
                    </div>
                    <button
                      onClick={() => removePick(pick.id)}
                      className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                      title="Remove pick"
                    >
                      <HiTrash className="w-4 h-4 hover:cursor-pointer" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons Container */}
          <div className="flex justify-center items-center gap-3 mt-8">
            <button
              onClick={handleOptimizeParlays}
              disabled={isOptimizing || totalPicksCount < 2}
              className={`px-6 py-3 font-bold rounded-lg text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl ${
                isOptimizing || totalPicksCount < 2
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:cursor-pointer'
              }`}
            >
              {isOptimizing ? 'Optimizing...' : 'Optimize Parlays'}
            </button>
            <button
              onClick={handleAddPicksClick}
              className="px-6 py-3 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
            >
              Add More Picks
            </button>
          </div>

          {/* Optimizer Results Section */}
          {(optimizerError || optimizerResult) && (
            <div className="mt-8 p-6 bg-gray-800 bg-opacity-80 border border-[#58C612] rounded-xl shadow-md">
              {optimizerError && (
                <p className="text-red-400 mb-2">
                  <strong className="text-red-300">Error:</strong> {optimizerError}
                </p>
              )}
              {optimizerResult && (
                <p className="text-[#58C612]">
                  <strong className="text-green-200">Optimizer Result:</strong> {optimizerResult}
                </p>
              )}
            </div>
          )}

          {/* Subparlays Grid */}
          {subparlays.length > 0 && (
            <div className="mt-8 p-6 bg-gray-900 bg-opacity-75 border border-[#58C612] rounded-xl shadow-lg">
              <h3 className="text-2xl font-semibold text-[#58C612] mb-4">
                Generated Subparlays
              </h3>
              <div className="flex flex-wrap gap-2">
                {subparlays.map((column, i) => {
                  let topLabelText = "Standard";
                  if (column.length >= 3) {
                    const oddsList = column.map((p) => parseFloat(p.odds));
                    const meanOdds =
                      oddsList.reduce((a, b) => a + b, 0) / oddsList.length;
                    let votes = { Flex: 0, Standard: 0 };
                    oddsList.forEach((odds) => {
                      if (odds - meanOdds >= 0.12) votes.Flex++;
                      else if (meanOdds - odds >= 0.12) votes.Standard++;
                    });
                    if (votes.Flex > votes.Standard) topLabelText = "Flex";
                  }
                  return (
                    <div
                      key={i}
                      className="flex flex-col items-center p-1 bg-gray-800 rounded"
                    >
                      <div className="text-xs font-bold text-green-200 mb-1">
                        {topLabelText}
                      </div>
                      <div className="flex flex-col border-2 border-gray-700 p-0.5 w-10 space-y-0.5">
                        {column.map((pick) => (
                          <div
                            key={pick.pID}
                            title={`ID: ${pick.pID}, Odds: ${
                              pick.odds
                            }, Confidence: ${pick.confidence?.toFixed(2)}%`}
                            className="w-full h-5 text-xxs flex items-center justify-center text-white font-mono"
                            style={{
                              backgroundColor: getConfidenceColor(
                                pick.confidence
                              ),
                            }}
                          >
                            {`#${pick.pID}`}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default HomePage;
